Index: _worker.js
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>let BOT_TOKEN;\r\nlet GROUP_ID;\r\nlet MAX_MESSAGES_PER_MINUTE;\r\n\r\nlet lastCleanupTime = 0;\r\nconst CLEANUP_INTERVAL = 24 * 60 * 60 * 1000; // 24 小时\r\nlet isInitialized = false;\r\nconst processedMessages = new Set();\r\nconst processedCallbacks = new Set();\r\n\r\nconst topicCreationLocks = new Map();\r\n\r\nconst settingsCache = new Map([\r\n  ['verification_enabled', null],\r\n  ['user_raw_enabled', null]\r\n]);\r\n\r\nclass LRUCache {\r\n  constructor(maxSize) {\r\n    this.maxSize = maxSize;\r\n    this.cache = new Map();\r\n  }\r\n  get(key) {\r\n    const value = this.cache.get(key);\r\n    if (value !== undefined) {\r\n      this.cache.delete(key);\r\n      this.cache.set(key, value);\r\n    }\r\n    return value;\r\n  }\r\n  set(key, value) {\r\n    if (this.cache.has(key)) {\r\n      this.cache.delete(key);\r\n    } else if (this.cache.size >= this.maxSize) {\r\n      const firstKey = this.cache.keys().next().value;\r\n      this.cache.delete(firstKey);\r\n    }\r\n    this.cache.set(key, value);\r\n  }\r\n  delete(key) {\r\n    this.cache.delete(key);\r\n  }\r\n  clear() {\r\n    this.cache.clear();\r\n  }\r\n}\r\n\r\nconst userInfoCache = new LRUCache(500);  // 减少缓存大小\r\nconst topicIdCache = new LRUCache(500);\r\nconst userStateCache = new LRUCache(500);\r\nconst messageRateCache = new LRUCache(500);\r\n\r\n// 添加错误重试配置\r\nconst RETRY_CONFIG = {\r\n  maxRetries: 3,\r\n  baseDelay: 1000,\r\n  maxDelay: 5000\r\n};\r\n\r\n// 硬编码的消息内容\r\nconst MESSAGES = {\r\n  START: `你好，欢迎使用私聊机器人！现在可以发送消息了。`,\r\n\r\n  VERIFICATION_SUCCESS: `✅ 验证成功！您现在可以与我聊天了。`,\r\n\r\n  NOTIFICATION: `\uD83D\uDEE1\uFE0F管理员面板按钮功能说明\r\n=================\r\n⚙\uFE0F通过在本群组中回复用户→/admin命令，可打开管理员面板，使用以下功能：\r\n\r\n--- \uD83D\uDD18按钮功能 ---\r\n➡\uFE0F拉黑用户：     将用户加入黑名单\r\n➡\uFE0F解除拉黑：     将用户从黑名单移除\r\n➡\uFE0F关闭验证码：   所有用户去除验证码。\r\n➡\uFE0F查询黑名单：   列出所有被禁用户 ID。\r\n➡\uFE0F删除用户：     删除用户数据库信息`,\r\n\r\n  ADMIN_TEST: `测试`\r\n};\r\n\r\nexport default {\r\n  async fetch(request, env) {\r\n    BOT_TOKEN = env.BOT_TOKEN_ENV || null;\r\n    GROUP_ID = env.GROUP_ID_ENV || null;\r\n    MAX_MESSAGES_PER_MINUTE = env.MAX_MESSAGES_PER_MINUTE_ENV ? parseInt(env.MAX_MESSAGES_PER_MINUTE_ENV) : 40;\r\n\r\n    if (!env.D1) {\r\n      return new Response('Server configuration error: D1 database is not bound', { status: 500 });\r\n    }\r\n\r\n    if (!isInitialized) {\r\n      await initialize(env.D1, request);\r\n      isInitialized = true;\r\n    }\r\n\r\n    async function handleRequest(request) {\r\n      if (!BOT_TOKEN || !GROUP_ID) {\r\n        return new Response('Server configuration error: Missing required environment variables', { status: 500 });\r\n      }\r\n\r\n      const url = new URL(request.url);\r\n      if (url.pathname === '/webhook') {\r\n        try {\r\n          const update = await request.json();\r\n          await handleUpdate(update);\r\n          return new Response('OK');\r\n        } catch (error) {\r\n          return new Response('Bad Request', { status: 400 });\r\n        }\r\n      } else if (url.pathname === '/registerWebhook') {\r\n        return await registerWebhook(request);\r\n      } else if (url.pathname === '/unRegisterWebhook') {\r\n        return await unRegisterWebhook();\r\n      } else if (url.pathname === '/checkTables') {\r\n        await checkAndRepairTables(env.D1);\r\n        return new Response('Database tables checked and repaired', { status: 200 });\r\n      }\r\n      return new Response('Not Found', { status: 404 });\r\n    }\r\n\r\n    async function initialize(d1, request) {\r\n      await Promise.all([\r\n        checkAndRepairTables(d1),\r\n        autoRegisterWebhook(request),\r\n        checkBotPermissions(),\r\n        cleanExpiredVerificationCodes(d1)\r\n      ]);\r\n    }\r\n\r\n    async function autoRegisterWebhook(request) {\r\n      const webhookUrl = `${new URL(request.url).origin}/webhook`;\r\n      await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/setWebhook`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ url: webhookUrl }),\r\n      });\r\n    }\r\n\r\n    async function checkBotPermissions() {\r\n      // 直接检查机器人成员状态，无需先调用 getChat\r\n      // getChatMember 会同时验证群组访问权限和机器人状态\r\n      const memberResponse = await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/getChatMember`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          chat_id: GROUP_ID,\r\n          user_id: (await getBotId())\r\n        })\r\n      });\r\n      const memberData = await memberResponse.json();\r\n      if (!memberData.ok) {\r\n        // Corrected error message typo\r\n        throw new Error(`Failed to get bot permissions: ${memberData.description}`);\r\n      }\r\n    }\r\n\r\n    async function getBotId() {\r\n      const response = await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/getMe`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({})\r\n      });\r\n      const data = await response.json();\r\n      if (!data.ok) throw new Error(`Failed to get bot ID: ${data.description}`);\r\n      return data.result.id;\r\n    }\r\n\r\n    async function checkAndRepairTables(d1) {\r\n      const expectedTables = {\r\n        user_states: {\r\n          columns: {\r\n            chat_id: 'TEXT PRIMARY KEY',\r\n            is_blocked: 'BOOLEAN DEFAULT FALSE',\r\n            is_verified: 'BOOLEAN DEFAULT FALSE',\r\n            verified_expiry: 'INTEGER',\r\n            verification_code: 'TEXT',\r\n            code_expiry: 'INTEGER',\r\n            last_verification_message_id: 'TEXT',\r\n            is_first_verification: 'BOOLEAN DEFAULT TRUE',\r\n            is_rate_limited: 'BOOLEAN DEFAULT FALSE',\r\n            is_verifying: 'BOOLEAN DEFAULT FALSE'\r\n          }\r\n        },\r\n        message_rates: {\r\n          columns: {\r\n            chat_id: 'TEXT PRIMARY KEY',\r\n            message_count: 'INTEGER DEFAULT 0',\r\n            window_start: 'INTEGER',\r\n            start_count: 'INTEGER DEFAULT 0',\r\n            start_window_start: 'INTEGER'\r\n          }\r\n        },\r\n        chat_topic_mappings: {\r\n          columns: {\r\n            chat_id: 'TEXT PRIMARY KEY',\r\n            topic_id: 'TEXT NOT NULL'\r\n          }\r\n        },\r\n        settings: {\r\n          columns: {\r\n            key: 'TEXT PRIMARY KEY',\r\n            value: 'TEXT'\r\n          }\r\n        }\r\n      };\r\n\r\n      for (const [tableName, structure] of Object.entries(expectedTables)) {\r\n        const tableInfo = await d1.prepare(\r\n            `SELECT sql FROM sqlite_master WHERE type='table' AND name=?`\r\n        ).bind(tableName).first();\r\n\r\n        if (!tableInfo) {\r\n          await createTable(d1, tableName, structure);\r\n          continue;\r\n        }\r\n\r\n        const columnsResult = await d1.prepare(\r\n            `PRAGMA table_info(${tableName})`\r\n        ).all();\r\n\r\n        const currentColumns = new Map(\r\n            columnsResult.results.map(col => [col.name, {\r\n              type: col.type,\r\n              notnull: col.notnull,\r\n              dflt_value: col.dflt_value\r\n            }])\r\n        );\r\n\r\n        for (const [colName, colDef] of Object.entries(structure.columns)) {\r\n          if (!currentColumns.has(colName)) {\r\n            const columnParts = colDef.split(' ');\r\n            const addColumnSQL = `ALTER TABLE ${tableName} ADD COLUMN ${colName} ${columnParts.slice(1).join(' ')}`;\r\n            await d1.exec(addColumnSQL);\r\n          }\r\n        }\r\n\r\n        if (tableName === 'settings') {\r\n          await d1.exec('CREATE INDEX IF NOT EXISTS idx_settings_key ON settings (key)');\r\n        }\r\n      }\r\n\r\n      await Promise.all([\r\n        d1.prepare('INSERT OR IGNORE INTO settings (key, value) VALUES (?, ?)')\r\n            .bind('verification_enabled', 'true').run(),\r\n        d1.prepare('INSERT OR IGNORE INTO settings (key, value) VALUES (?, ?)')\r\n            .bind('user_raw_enabled', 'true').run()\r\n      ]);\r\n\r\n      settingsCache.set('verification_enabled', await getSetting('verification_enabled', d1));\r\n      settingsCache.set('user_raw_enabled', await getSetting('user_raw_enabled', d1));\r\n    }\r\n\r\n    async function createTable(d1, tableName, structure) {\r\n      const columnsDef = Object.entries(structure.columns)\r\n          .map(([name, def]) => `${name} ${def}`)\r\n          .join(', ');\r\n      const createSQL = `CREATE TABLE ${tableName} (${columnsDef})`;\r\n      await d1.exec(createSQL);\r\n    }\r\n\r\n    async function cleanExpiredVerificationCodes(d1) {\r\n      const now = Date.now();\r\n      if (now - lastCleanupTime < CLEANUP_INTERVAL) {\r\n        return;\r\n      }\r\n\r\n      const nowSeconds = Math.floor(now / 1000);\r\n      const expiredCodes = await d1.prepare(\r\n          'SELECT chat_id FROM user_states WHERE code_expiry IS NOT NULL AND code_expiry < ?'\r\n      ).bind(nowSeconds).all();\r\n\r\n      if (expiredCodes.results.length > 0) {\r\n        await d1.batch(\r\n            expiredCodes.results.map(({ chat_id }) =>\r\n                d1.prepare(\r\n                    'UPDATE user_states SET verification_code = NULL, code_expiry = NULL, is_verifying = FALSE WHERE chat_id = ?'\r\n                ).bind(chat_id)\r\n            )\r\n        );\r\n      }\r\n      lastCleanupTime = now;\r\n    }\r\n\r\n    async function handleUpdate(update) {\r\n      try {\r\n        if (update.message) {\r\n          const messageKey = `${update.message.chat.id}_${update.message.message_id}`;\r\n          if (processedMessages.has(messageKey)) return;\r\n          processedMessages.add(messageKey);\r\n\r\n          // 清理旧消息记录\r\n          if (processedMessages.size > 1000) {\r\n            const oldMessages = Array.from(processedMessages).slice(0, 500);\r\n            oldMessages.forEach(key => processedMessages.delete(key));\r\n          }\r\n\r\n          await onMessage(update.message);\r\n        } else if (update.callback_query) {\r\n          const callbackKey = update.callback_query.id;\r\n          if (processedCallbacks.has(callbackKey)) return;\r\n          processedCallbacks.add(callbackKey);\r\n\r\n          // 清理旧回调记录\r\n          if (processedCallbacks.size > 1000) {\r\n            const oldCallbacks = Array.from(processedCallbacks).slice(0, 500);\r\n            oldCallbacks.forEach(key => processedCallbacks.delete(key));\r\n          }\r\n\r\n          await onCallbackQuery(update.callback_query);\r\n        }\r\n      } catch (error) {\r\n        console.error('Update handling failed:', error);\r\n      }\r\n    }\r\n\r\n    async function onMessage(message) {\r\n      const chatId = message.chat.id.toString();\r\n      const text = message.text || '';\r\n      const messageId = message.message_id;\r\n\r\n      if (chatId === GROUP_ID) {\r\n        const topicId = message.message_thread_id;\r\n        if (topicId) {\r\n          const privateChatId = await getPrivateChatId(topicId);\r\n          if (privateChatId && text === '/admin') {\r\n            await sendAdminPanel(chatId, topicId, privateChatId, messageId);\r\n            return;\r\n          }\r\n          if (privateChatId && text.startsWith('/reset_user')) {\r\n            await handleResetUser(chatId, topicId, text);\r\n            return;\r\n          }\r\n          if (privateChatId) {\r\n            await forwardMessageToPrivateChat(privateChatId, message);\r\n          }\r\n        }\r\n        return;\r\n      }\r\n\r\n      let userState = userStateCache.get(chatId);\r\n      if (userState === undefined) {\r\n        userState = await env.D1.prepare('SELECT is_blocked, is_first_verification, is_verified, verified_expiry, is_verifying FROM user_states WHERE chat_id = ?')\r\n            .bind(chatId)\r\n            .first();\r\n        if (!userState) {\r\n          userState = { is_blocked: false, is_first_verification: true, is_verified: false, verified_expiry: null, is_verifying: false };\r\n          await env.D1.prepare('INSERT INTO user_states (chat_id, is_blocked, is_first_verification, is_verified, is_verifying) VALUES (?, ?, ?, ?, ?)')\r\n              .bind(chatId, false, true, false, false)\r\n              .run();\r\n        }\r\n        userStateCache.set(chatId, userState);\r\n      }\r\n\r\n      if (userState.is_blocked) {\r\n        await sendMessageToUser(chatId, \"您已被拉黑，无法发送消息。请联系管理员解除拉黑。\");\r\n        return;\r\n      }\r\n\r\n      // 使用缓存的设置值，减少数据库查询\r\n      let verificationEnabled = await getSetting('verification_enabled', env.D1);\r\n\r\n      if (!verificationEnabled) {\r\n        // 验证码关闭时，所有用户都可以直接发送消息\r\n      } else {\r\n        const nowSeconds = Math.floor(Date.now() / 1000);\r\n        const isVerified = userState.is_verified && userState.verified_expiry && nowSeconds < userState.verified_expiry;\r\n        const isFirstVerification = userState.is_first_verification;\r\n        const isRateLimited = await checkMessageRate(chatId);\r\n        const isVerifying = userState.is_verifying || false;\r\n\r\n        if (!isVerified || (isRateLimited && !isFirstVerification)) {\r\n          if (isVerifying) {\r\n            // 检查验证码是否已过期\r\n            const storedCode = await env.D1.prepare('SELECT verification_code, code_expiry FROM user_states WHERE chat_id = ?')\r\n                .bind(chatId)\r\n                .first();\r\n\r\n            const nowSeconds = Math.floor(Date.now() / 1000);\r\n            const isCodeExpired = !storedCode?.verification_code || !storedCode?.code_expiry || nowSeconds > storedCode.code_expiry;\r\n\r\n            if (isCodeExpired) {\r\n              await handleExpiredVerification(chatId);\r\n              return;\r\n            } else {\r\n              await sendMessageToUser(chatId, `请完成验证后发送消息\"${text || '您的具体信息'}\"。`);\r\n            }\r\n            return;\r\n          }\r\n          await sendMessageToUser(chatId, `请完成验证后发送消息\"${text || '您的具体信息'}\"。`);\r\n          await handleVerification(chatId);\r\n          return;\r\n        }\r\n      }\r\n\r\n      if (text === '/start') {\r\n        if (await checkStartCommandRate(chatId)) {\r\n          await sendMessageToUser(chatId, \"您发送 /start 命令过于频繁，请稍后再试！\");\r\n          return;\r\n        }\r\n\r\n        const successMessage = await getVerificationSuccessMessage();\r\n        await sendMessageToUser(chatId, `${successMessage}\\n你好，欢迎使用私聊机器人，现在发送信息吧！`);\r\n        const userInfo = await getUserInfo(chatId);\r\n        await ensureUserTopic(chatId, userInfo);\r\n        return;\r\n      }\r\n\r\n      const userInfo = await getUserInfo(chatId);\r\n      if (!userInfo) {\r\n        await sendMessageToUser(chatId, \"无法获取用户信息，请稍后再试或联系管理员。\");\r\n        return;\r\n      }\r\n\r\n      let topicId = await ensureUserTopic(chatId, userInfo);\r\n      if (!topicId) {\r\n        await sendMessageToUser(chatId, \"无法创建话题，请稍后再试或联系管理员。\");\r\n        return;\r\n      }\r\n\r\n      const userName = userInfo.username || `User_${chatId}`;\r\n      const nickname = userInfo.nickname || userName;\r\n\r\n      try {\r\n        if (text) {\r\n          const formattedMessage = `${nickname}:\\n${text}`;\r\n          await sendMessageToTopic(topicId, formattedMessage);\r\n        } else {\r\n          await copyMessageToTopic(topicId, message);\r\n        }\r\n      } catch (error) {\r\n        const errorMessage = error.message.toLowerCase();\r\n        if (errorMessage.includes('message thread not found') || errorMessage.includes('topic not found')) {\r\n          console.log(`Topic ${topicId} for chat ${chatId} is invalid, recreating.`);\r\n          await env.D1.prepare('DELETE FROM chat_topic_mappings WHERE chat_id = ?').bind(chatId).run();\r\n          topicIdCache.set(chatId, undefined);\r\n\r\n          const newTopicId = await ensureUserTopic(chatId, userInfo);\r\n          if (newTopicId) {\r\n            if (text) {\r\n              await sendMessageToTopic(newTopicId, `${nickname}:\\n${text}`);\r\n            } else {\r\n              await copyMessageToTopic(newTopicId, message);\r\n            }\r\n          } else {\r\n            await sendMessageToUser(chatId, \"无法重新创建话题，请稍后再试或联系管理员。\");\r\n          }\r\n        } else {\r\n          console.error(`Failed to send message to topic ${topicId}:`, error);\r\n          await sendMessageToUser(chatId, \"发送消息失败，请稍后重试。\");\r\n        }\r\n      }\r\n    }\r\n\r\n    async function handleExpiredVerification(chatId, messageId = null) {\r\n      await sendMessageToUser(chatId, '验证码已过期，正在为您发送新的验证码...');\r\n\r\n      // 重置验证状态\r\n      await env.D1.prepare('UPDATE user_states SET verification_code = NULL, code_expiry = NULL, is_verifying = FALSE WHERE chat_id = ?')\r\n          .bind(chatId)\r\n          .run();\r\n      const userState = userStateCache.get(chatId) || {};\r\n      userState.verification_code = null;\r\n      userState.code_expiry = null;\r\n      userState.is_verifying = false;\r\n      userStateCache.set(chatId, userState);\r\n\r\n      // 删除旧的验证消息\r\n      const lastMessageId = messageId || (await env.D1.prepare('SELECT last_verification_message_id FROM user_states WHERE chat_id = ?').bind(chatId).first())?.last_verification_message_id;\r\n      if (lastMessageId) {\r\n        try {\r\n          await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/deleteMessage`, {\r\n            method: 'POST',\r\n            headers: { 'Content-Type': 'application/json' },\r\n            body: JSON.stringify({ chat_id: chatId, message_id: lastMessageId })\r\n          });\r\n        } catch (deleteError) {\r\n          console.log(`删除旧验证消息失败: ${deleteError.message}`);\r\n        } finally {\r\n          await env.D1.prepare('UPDATE user_states SET last_verification_message_id = NULL WHERE chat_id = ?').bind(chatId).run();\r\n          if (userState.last_verification_message_id) {\r\n            userState.last_verification_message_id = null;\r\n            userStateCache.set(chatId, userState);\r\n          }\r\n        }\r\n      }\r\n\r\n      // 立即发送新的验证码\r\n      try {\r\n        await handleVerification(chatId);\r\n      } catch (verificationError) {\r\n        console.error(`发送新验证码失败: ${verificationError.message}`);\r\n        setTimeout(async () => {\r\n          try {\r\n            await handleVerification(chatId);\r\n          } catch (retryError) {\r\n            console.error(`重试发送验证码仍失败: ${retryError.message}`);\r\n            await sendMessageToUser(chatId, '发送验证码失败，请发送任意消息重试');\r\n          }\r\n        }, 1000);\r\n      }\r\n    }\r\n\r\n    async function ensureUserTopic(chatId, userInfo) {\r\n      let lock = topicCreationLocks.get(chatId);\r\n      if (lock) {\r\n        return await lock;\r\n      }\r\n\r\n      let topicId = await getExistingTopicId(chatId);\r\n      if (topicId) {\r\n        return topicId;\r\n      }\r\n\r\n      const newLock = (async () => {\r\n        try {\r\n          const userName = userInfo.username || `User_${chatId}`;\r\n          const nickname = userInfo.nickname || userName;\r\n          const newTopicId = await createForumTopic(userName, nickname, userInfo.id || chatId);\r\n          await saveTopicId(chatId, newTopicId);\r\n          return newTopicId;\r\n        } finally {\r\n          topicCreationLocks.delete(chatId);\r\n        }\r\n      })();\r\n\r\n      topicCreationLocks.set(chatId, newLock);\r\n      return await newLock;\r\n    }\r\n\r\n    async function handleResetUser(chatId, topicId, text) {\r\n      const senderId = chatId;\r\n      const isAdmin = await checkIfAdmin(senderId);\r\n      if (!isAdmin) {\r\n        await sendMessageToTopic(topicId, '只有管理员可以使用此功能。');\r\n        return;\r\n      }\r\n\r\n      const parts = text.split(' ');\r\n      if (parts.length !== 2) {\r\n        await sendMessageToTopic(topicId, '用法：/reset_user <chat_id>');\r\n        return;\r\n      }\r\n\r\n      const targetChatId = parts[1];\r\n      await env.D1.batch([\r\n        env.D1.prepare('DELETE FROM user_states WHERE chat_id = ?').bind(targetChatId),\r\n        env.D1.prepare('DELETE FROM message_rates WHERE chat_id = ?').bind(targetChatId),\r\n        env.D1.prepare('DELETE FROM chat_topic_mappings WHERE chat_id = ?').bind(targetChatId)\r\n      ]);\r\n      userStateCache.delete(targetChatId);\r\n      messageRateCache.delete(targetChatId);\r\n      topicIdCache.delete(targetChatId);\r\n      await sendMessageToTopic(topicId, `用户 ${targetChatId} 的状态已重置。`);\r\n    }\r\n\r\n    async function sendAdminPanel(chatId, topicId, privateChatId, messageId) {\r\n      const verificationEnabled = await getSetting('verification_enabled', env.D1);\r\n      const userRawEnabled = await getSetting('user_raw_enabled', env.D1);\r\n\r\n      const buttons = [\r\n        [\r\n          { text: '拉黑用户', callback_data: `block_${privateChatId}` },\r\n          { text: '解除拉黑', callback_data: `unblock_${privateChatId}` }\r\n        ],\r\n        [\r\n          { text: verificationEnabled ? '关闭验证码' : '开启验证码', callback_data: `toggle_verification_${privateChatId}` },\r\n          { text: '查询黑名单', callback_data: `check_blocklist_${privateChatId}` }\r\n        ],\r\n        [\r\n          { text: userRawEnabled ? '关闭用户Raw' : '开启用户Raw', callback_data: `toggle_user_raw_${privateChatId}` }\r\n        ],\r\n        [\r\n          { text: '删除用户', callback_data: `delete_user_${privateChatId}` }\r\n        ]\r\n      ];\r\n\r\n      const adminMessage = '管理员面板：请选择操作';\r\n      await Promise.all([\r\n        fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/sendMessage`, {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({\r\n            chat_id: chatId,\r\n            message_thread_id: topicId,\r\n            text: adminMessage,\r\n            reply_markup: { inline_keyboard: buttons }\r\n          })\r\n        }),\r\n        fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/deleteMessage`, {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({\r\n            chat_id: chatId,\r\n            message_id: messageId\r\n          })\r\n        })\r\n      ]);\r\n    }\r\n\r\n    async function getVerificationSuccessMessage(chatId = null) {\r\n      let message = MESSAGES.VERIFICATION_SUCCESS;\r\n\r\n      // 如果提供了chatId，检查是否为管理员\r\n      if (chatId) {\r\n        const isAdmin = await checkIfAdmin(chatId);\r\n        const userRawEnabled = await getSetting('user_raw_enabled', env.D1);\r\n\r\n        if (isAdmin && userRawEnabled) {\r\n          message += `\\n\\n\uD83D\uDCCB 管理员信息：\\n您拥有管理员权限，可以使用高级功能。`;\r\n        }\r\n      }\r\n\r\n      return message;\r\n    }\r\n\r\n    async function getNotificationContent() {\r\n      return MESSAGES.NOTIFICATION;\r\n    }\r\n\r\n    async function checkStartCommandRate(chatId) {\r\n      const now = Date.now();\r\n      const window = 5 * 60 * 1000;\r\n      const maxStartsPerWindow = 1;\r\n\r\n      await env.D1.prepare('INSERT OR IGNORE INTO message_rates (chat_id) VALUES (?)').bind(chatId).run();\r\n\r\n      const result = await env.D1.prepare(\r\n         `UPDATE message_rates\r\n          SET\r\n            start_count = CASE\r\n              WHEN ? - COALESCE(start_window_start, 0) > ? THEN 1\r\n              ELSE start_count + 1\r\n            END,\r\n            start_window_start = CASE\r\n              WHEN ? - COALESCE(start_window_start, 0) > ? THEN ?\r\n              ELSE COALESCE(start_window_start, ?)\r\n            END\r\n          WHERE chat_id = ?\r\n          RETURNING start_count, start_window_start`\r\n      ).bind(now, window, now, window, now, now, chatId).first();\r\n\r\n      if (!result) {\r\n        console.error(`Failed to update start command rate for chat_id: ${chatId}`);\r\n        return true; // Fail safe, assume rate limited.\r\n      }\r\n\r\n      const cachedData = messageRateCache.get(chatId) || {};\r\n      messageRateCache.set(chatId, {\r\n        ...cachedData,\r\n        start_count: result.start_count,\r\n        start_window_start: result.start_window_start,\r\n      });\r\n\r\n      return result.start_count > maxStartsPerWindow;\r\n    }\r\n\r\n    async function checkMessageRate(chatId) {\r\n      const now = Date.now();\r\n      const window = 60 * 1000;\r\n\r\n      await env.D1.prepare('INSERT OR IGNORE INTO message_rates (chat_id) VALUES (?)').bind(chatId).run();\r\n\r\n      const result = await env.D1.prepare(\r\n         `UPDATE message_rates\r\n          SET\r\n            message_count = CASE\r\n              WHEN ? - COALESCE(window_start, 0) > ? THEN 1\r\n              ELSE message_count + 1\r\n            END,\r\n            window_start = CASE\r\n              WHEN ? - COALESCE(window_start, 0) > ? THEN ?\r\n              ELSE COALESCE(window_start, ?)\r\n            END\r\n          WHERE chat_id = ?\r\n          RETURNING message_count, window_start`\r\n      ).bind(now, window, now, window, now, now, chatId).first();\r\n\r\n      if (!result) {\r\n        console.error(`Failed to update message rate for chat_id: ${chatId}`);\r\n        return true; // Fail safe, assume rate limited.\r\n      }\r\n\r\n      const cachedData = messageRateCache.get(chatId) || {};\r\n      messageRateCache.set(chatId, {\r\n        ...cachedData,\r\n        message_count: result.message_count,\r\n        window_start: result.window_start,\r\n      });\r\n\r\n      return result.message_count > MAX_MESSAGES_PER_MINUTE;\r\n    }\r\n\r\n    async function getSetting(key, d1) {\r\n      if (settingsCache.has(key)) {\r\n        const cachedValue = settingsCache.get(key);\r\n        if (cachedValue !== null) {\r\n          return cachedValue; // Return boolean from cache\r\n        }\r\n      }\r\n\r\n      const result = await d1.prepare('SELECT value FROM settings WHERE key = ?')\r\n          .bind(key)\r\n          .first();\r\n\r\n      // Default to true if not set, as that's the initial state.\r\n      const value = result ? result.value === 'true' : true;\r\n\r\n      settingsCache.set(key, value);\r\n      return value;\r\n    }\r\n\r\n    async function setSetting(key, value) {\r\n      await env.D1.prepare('INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)')\r\n          .bind(key, value)\r\n          .run();\r\n      if (key === 'verification_enabled') {\r\n        settingsCache.set('verification_enabled', value === 'true');\r\n        if (value === 'false') {\r\n          const nowSeconds = Math.floor(Date.now() / 1000);\r\n          const verifiedExpiry = nowSeconds + 3600 * 24;\r\n          await env.D1.prepare('UPDATE user_states SET is_verified = ?, verified_expiry = ?, is_verifying = ?, verification_code = NULL, code_expiry = NULL, is_first_verification = ? WHERE is_blocked = FALSE')\r\n              .bind(true, verifiedExpiry, false, false)\r\n              .run();\r\n          userStateCache.clear();\r\n        }\r\n      } else if (key === 'user_raw_enabled') {\r\n        settingsCache.set('user_raw_enabled', value === 'true');\r\n      }\r\n    }\r\n\r\n    async function onCallbackQuery(callbackQuery) {\r\n      const chatId = callbackQuery.message.chat.id.toString();\r\n      const topicId = callbackQuery.message.message_thread_id;\r\n      const data = callbackQuery.data;\r\n      const messageId = callbackQuery.message.message_id;\r\n      const callbackKey = `${chatId}:${callbackQuery.id}`;\r\n\r\n      if (processedCallbacks.has(callbackKey)) {\r\n        return;\r\n      }\r\n      processedCallbacks.add(callbackKey);\r\n\r\n      const parts = data.split('_');\r\n      let action;\r\n      let privateChatId;\r\n\r\n      if (data.startsWith('verify_')) {\r\n        action = 'verify';\r\n        privateChatId = parts[1];\r\n      } else if (data.startsWith('toggle_verification_')) {\r\n        action = 'toggle_verification';\r\n        privateChatId = parts.slice(2).join('_');\r\n      } else if (data.startsWith('toggle_user_raw_')) {\r\n        action = 'toggle_user_raw';\r\n        privateChatId = parts.slice(3).join('_');\r\n      } else if (data.startsWith('check_blocklist_')) {\r\n        action = 'check_blocklist';\r\n        privateChatId = parts.slice(2).join('_');\r\n      } else if (data.startsWith('block_')) {\r\n        action = 'block';\r\n        privateChatId = parts.slice(1).join('_');\r\n      } else if (data.startsWith('unblock_')) {\r\n        action = 'unblock';\r\n        privateChatId = parts.slice(1).join('_');\r\n      } else if (data.startsWith('delete_user_')) {\r\n        action = 'delete_user';\r\n        privateChatId = parts.slice(2).join('_');\r\n      } else {\r\n        action = data;\r\n        privateChatId = '';\r\n      }\r\n\r\n      if (action === 'verify') {\r\n        const [, userChatId, , result] = data.split('_');\r\n        if (userChatId !== chatId) {\r\n          return;\r\n        }\r\n\r\n        let verificationState = userStateCache.get(chatId);\r\n        if (verificationState === undefined) {\r\n          verificationState = await env.D1.prepare('SELECT verification_code, code_expiry, is_verifying FROM user_states WHERE chat_id = ?')\r\n              .bind(chatId)\r\n              .first();\r\n          if (!verificationState) {\r\n            verificationState = { verification_code: null, code_expiry: null, is_verifying: false };\r\n          }\r\n          userStateCache.set(chatId, verificationState);\r\n        }\r\n\r\n        const storedCode = verificationState.verification_code;\r\n        const codeExpiry = verificationState.code_expiry;\r\n        const nowSeconds = Math.floor(Date.now() / 1000);\r\n\r\n        if (!storedCode || (codeExpiry && nowSeconds > codeExpiry)) {\r\n          await handleExpiredVerification(chatId, messageId);\r\n          return;\r\n        }\r\n\r\n        if (result === 'correct') {\r\n          const verifiedExpiry = nowSeconds + 3600 * 24;\r\n          await env.D1.prepare('UPDATE user_states SET is_verified = ?, verified_expiry = ?, verification_code = NULL, code_expiry = NULL, last_verification_message_id = NULL, is_first_verification = ?, is_verifying = ? WHERE chat_id = ?')\r\n              .bind(true, verifiedExpiry, false, false, chatId)\r\n              .run();\r\n          verificationState = await env.D1.prepare('SELECT is_verified, verified_expiry, verification_code, code_expiry, last_verification_message_id, is_first_verification, is_verifying FROM user_states WHERE chat_id = ?')\r\n              .bind(chatId)\r\n              .first();\r\n          userStateCache.set(chatId, verificationState);\r\n\r\n          let rateData = await env.D1.prepare('SELECT message_count, window_start FROM message_rates WHERE chat_id = ?')\r\n              .bind(chatId)\r\n              .first() || { message_count: 0, window_start: nowSeconds * 1000 };\r\n          rateData.message_count = 0;\r\n          rateData.window_start = nowSeconds * 1000;\r\n          messageRateCache.set(chatId, rateData);\r\n          await env.D1.prepare('UPDATE message_rates SET message_count = ?, window_start = ? WHERE chat_id = ?')\r\n              .bind(0, nowSeconds * 1000, chatId)\r\n              .run();\r\n\r\n          const successMessage = await getVerificationSuccessMessage(chatId);\r\n          await sendMessageToUser(chatId, `${successMessage}\\n你好，欢迎使用私聊机器人！现在可以发送消息了。`);\r\n          const userInfo = await getUserInfo(chatId);\r\n          await ensureUserTopic(chatId, userInfo);\r\n        } else {\r\n          await sendMessageToUser(chatId, '验证失败，请重新尝试。');\r\n          await handleVerification(chatId);\r\n        }\r\n\r\n        await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/deleteMessage`, {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({\r\n            chat_id: chatId,\r\n            message_id: messageId\r\n          })\r\n        });\r\n      } else {\r\n        const senderId = callbackQuery.from.id.toString();\r\n        const isAdmin = await checkIfAdmin(senderId);\r\n        if (!isAdmin) {\r\n          await sendMessageToTopic(topicId, '只有管理员可以使用此功能。');\r\n          await sendAdminPanel(chatId, topicId, privateChatId, messageId);\r\n          return;\r\n        }\r\n\r\n        if (action === 'block') {\r\n          await env.D1.prepare('UPDATE user_states SET is_blocked = ? WHERE chat_id = ?')\r\n              .bind(true, privateChatId)\r\n              .run();\r\n          const state = userStateCache.get(privateChatId) || {};\r\n          state.is_blocked = true;\r\n          userStateCache.set(privateChatId, state);\r\n          await sendMessageToTopic(topicId, `用户 ${privateChatId} 已被拉黑，消息将不再转发。`);\r\n        } else if (action === 'unblock') {\r\n          await env.D1.prepare('UPDATE user_states SET is_blocked = ?, is_first_verification = ? WHERE chat_id = ?')\r\n              .bind(false, true, privateChatId)\r\n              .run();\r\n          const state = userStateCache.get(privateChatId) || {};\r\n          state.is_blocked = false;\r\n          state.is_first_verification = true;\r\n          userStateCache.set(privateChatId, state);\r\n          await sendMessageToTopic(topicId, `用户 ${privateChatId} 已解除拉黑，消息将继续转发。`);\r\n        } else if (action === 'toggle_verification') {\r\n          const currentState = await getSetting('verification_enabled', env.D1);\r\n          const newState = !currentState;\r\n          await setSetting('verification_enabled', newState.toString());\r\n          await sendMessageToTopic(topicId, `验证码功能已${newState ? '开启' : '关闭'}。`);\r\n        } else if (action === 'check_blocklist') {\r\n          const blockedUsers = await env.D1.prepare('SELECT chat_id FROM user_states WHERE is_blocked = ?')\r\n              .bind(true)\r\n              .all();\r\n          const blockList = blockedUsers.results.length > 0\r\n              ? blockedUsers.results.map(row => row.chat_id).join('\\n')\r\n              : '当前没有被拉黑的用户。';\r\n          await sendMessageToTopic(topicId, `黑名单列表：\\n${blockList}`);\r\n        } else if (action === 'toggle_user_raw') {\r\n          const currentState = await getSetting('user_raw_enabled', env.D1);\r\n          const newState = !currentState;\r\n          await setSetting('user_raw_enabled', newState.toString());\r\n          await sendMessageToTopic(topicId, `用户端 Raw 链接已${newState ? '开启' : '关闭'}。`);\r\n        } else if (action === 'delete_user') {\r\n          userStateCache.delete(privateChatId);\r\n          messageRateCache.delete(privateChatId);\r\n          topicIdCache.delete(privateChatId);\r\n          await env.D1.batch([\r\n            env.D1.prepare('DELETE FROM user_states WHERE chat_id = ?').bind(privateChatId),\r\n            env.D1.prepare('DELETE FROM message_rates WHERE chat_id = ?').bind(privateChatId),\r\n            env.D1.prepare('DELETE FROM chat_topic_mappings WHERE chat_id = ?').bind(privateChatId)\r\n          ]);\r\n          await sendMessageToTopic(topicId, `用户 ${privateChatId} 的状态、消息记录和话题映射已删除，用户需重新发起会话。`);\r\n        } else {\r\n          await sendMessageToTopic(topicId, `未知操作：${action}`);\r\n        }\r\n\r\n        await sendAdminPanel(chatId, topicId, privateChatId, messageId);\r\n      }\r\n\r\n      await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/answerCallbackQuery`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          callback_query_id: callbackQuery.id\r\n        })\r\n      });\r\n    }\r\n\r\n    async function handleVerification(chatId) {\r\n      try {\r\n        let userState = userStateCache.get(chatId);\r\n        if (userState === undefined) {\r\n          userState = await env.D1.prepare('SELECT is_blocked, is_first_verification, is_verified, verified_expiry, is_verifying FROM user_states WHERE chat_id = ?')\r\n              .bind(chatId)\r\n              .first();\r\n          if (!userState) {\r\n            userState = { is_blocked: false, is_first_verification: true, is_verified: false, verified_expiry: null, is_verifying: false };\r\n          }\r\n          userStateCache.set(chatId, userState);\r\n        }\r\n\r\n        userState.verification_code = null;\r\n        userState.code_expiry = null;\r\n        userState.is_verifying = true;\r\n        userStateCache.set(chatId, userState);\r\n        await env.D1.prepare('UPDATE user_states SET verification_code = NULL, code_expiry = NULL, is_verifying = ? WHERE chat_id = ?')\r\n            .bind(true, chatId)\r\n            .run();\r\n\r\n        const lastVerification = userState.last_verification_message_id || (await env.D1.prepare('SELECT last_verification_message_id FROM user_states WHERE chat_id = ?')\r\n            .bind(chatId)\r\n            .first())?.last_verification_message_id;\r\n\r\n        if (lastVerification) {\r\n          try {\r\n            await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/deleteMessage`, {\r\n              method: 'POST',\r\n              headers: { 'Content-Type': 'application/json' },\r\n              body: JSON.stringify({\r\n                chat_id: chatId,\r\n                message_id: lastVerification\r\n              })\r\n            });\r\n          } catch (deleteError) {\r\n            console.log(`删除上一条验证消息失败: ${deleteError.message}`);\r\n            // 继续处理，即使删除失败\r\n          }\r\n\r\n          userState.last_verification_message_id = null;\r\n          userStateCache.set(chatId, userState);\r\n          await env.D1.prepare('UPDATE user_states SET last_verification_message_id = NULL WHERE chat_id = ?')\r\n              .bind(chatId)\r\n              .run();\r\n        }\r\n\r\n        // 确保发送验证码\r\n        await sendVerification(chatId);\r\n      } catch (error) {\r\n        console.error(`处理验证过程失败: ${error.message}`);\r\n        // 重置用户状态以防卡住\r\n        try {\r\n          await env.D1.prepare('UPDATE user_states SET is_verifying = FALSE WHERE chat_id = ?')\r\n              .bind(chatId)\r\n              .run();\r\n          let currentState = userStateCache.get(chatId);\r\n          if (currentState) {\r\n            currentState.is_verifying = false;\r\n            userStateCache.set(chatId, currentState);\r\n          }\r\n        } catch (resetError) {\r\n          console.error(`重置用户验证状态失败: ${resetError.message}`);\r\n        }\r\n        throw error; // 向上传递错误以便调用方处理\r\n      }\r\n    }\r\n\r\n    async function sendVerification(chatId) {\r\n      try {\r\n        const num1 = Math.floor(Math.random() * 10);\r\n        const num2 = Math.floor(Math.random() * 10);\r\n        const operation = Math.random() > 0.5 ? '+' : '-';\r\n        const correctResult = operation === '+' ? num1 + num2 : num1 - num2;\r\n\r\n        // 生成选项\r\n        const options = new Set([correctResult]);\r\n        while (options.size < 4) {\r\n          const wrongResult = correctResult + Math.floor(Math.random() * 5) - 2;\r\n          if (wrongResult !== correctResult && wrongResult >= -10 && wrongResult <= 20) {\r\n            options.add(wrongResult);\r\n          }\r\n        }\r\n        const optionArray = Array.from(options).sort(() => Math.random() - 0.5);\r\n\r\n        const buttons = [optionArray.map(option => ({\r\n          text: option.toString(),\r\n          callback_data: `verify_${chatId}_${option}_${option === correctResult ? 'correct' : 'wrong'}`\r\n        }))];\r\n\r\n        const question = `\uD83D\uDD22 请完成验证：${num1} ${operation} ${num2} = ?\\n⏰ 验证码5分钟内有效`;\r\n        const nowSeconds = Math.floor(Date.now() / 1000);\r\n        const codeExpiry = nowSeconds + 300;\r\n\r\n        const response = await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/sendMessage`, {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({\r\n            chat_id: chatId,\r\n            text: question,\r\n            reply_markup: {\r\n              inline_keyboard: buttons\r\n            }\r\n          })\r\n        });\r\n\r\n        const data = await response.json();\r\n        if (data.ok) {\r\n          // 更新用户状态\r\n          let userState = userStateCache.get(chatId) || {};\r\n          userState.verification_code = correctResult.toString();\r\n          userState.code_expiry = codeExpiry;\r\n          userState.last_verification_message_id = data.result.message_id.toString();\r\n          userState.is_verifying = true;\r\n          userStateCache.set(chatId, userState);\r\n\r\n          await env.D1.prepare('UPDATE user_states SET verification_code = ?, code_expiry = ?, last_verification_message_id = ?, is_verifying = ? WHERE chat_id = ?')\r\n              .bind(correctResult.toString(), codeExpiry, data.result.message_id.toString(), true, chatId)\r\n              .run();\r\n        } else {\r\n          throw new Error(`发送验证码失败: ${data.description}`);\r\n        }\r\n      } catch (error) {\r\n        console.error(`验证码发送错误: ${error.message}`);\r\n        throw error;\r\n      }\r\n    }\r\n\r\n    async function checkIfAdmin(userId) {\r\n      const response = await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/getChatMember`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          chat_id: GROUP_ID,\r\n          user_id: userId\r\n        })\r\n      });\r\n      const data = await response.json();\r\n      return data.ok && (data.result.status === 'administrator' || data.result.status === 'creator');\r\n    }\r\n\r\n    async function getUserInfo(chatId) {\r\n      let userInfo = userInfoCache.get(chatId);\r\n      if (userInfo !== undefined) {\r\n        return userInfo;\r\n      }\r\n\r\n      const response = await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/getChat`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ chat_id: chatId })\r\n      });\r\n      const data = await response.json();\r\n      if (!data.ok) {\r\n        userInfo = {\r\n          id: chatId,\r\n          username: `User_${chatId}`,\r\n          nickname: `User_${chatId}`\r\n        };\r\n      } else {\r\n        const result = data.result;\r\n        const nickname = result.first_name\r\n            ? `${result.first_name}${result.last_name ? ` ${result.last_name}` : ''}`.trim()\r\n            : result.username || `User_${chatId}`;\r\n        userInfo = {\r\n          id: result.id || chatId,\r\n          username: result.username || `User_${chatId}`,\r\n          nickname: nickname\r\n        };\r\n      }\r\n\r\n      userInfoCache.set(chatId, userInfo);\r\n      return userInfo;\r\n    }\r\n\r\n    async function getExistingTopicId(chatId) {\r\n      let topicId = topicIdCache.get(chatId);\r\n      if (topicId !== undefined) {\r\n        return topicId;\r\n      }\r\n\r\n      const result = await env.D1.prepare('SELECT topic_id FROM chat_topic_mappings WHERE chat_id = ?')\r\n          .bind(chatId)\r\n          .first();\r\n      topicId = result?.topic_id || null;\r\n      if (topicId) {\r\n        topicIdCache.set(chatId, topicId);\r\n      }\r\n      return topicId;\r\n    }\r\n\r\n    // 格式化时间为指定时区（默认为上海时间）\r\n    function formatTimeWithTimezone(date = new Date(), timezone = 'Asia/Shanghai') {\r\n      try {\r\n        // 使用 Intl.DateTimeFormat 来处理时区转换\r\n        const formatter = new Intl.DateTimeFormat('zh-CN', {\r\n          timeZone: timezone,\r\n          year: 'numeric',\r\n          month: '2-digit',\r\n          day: '2-digit',\r\n          hour: '2-digit',\r\n          minute: '2-digit',\r\n          second: '2-digit',\r\n          hour12: false\r\n        });\r\n\r\n        const parts = formatter.formatToParts(date);\r\n        const year = parts.find(part => part.type === 'year').value;\r\n        const month = parts.find(part => part.type === 'month').value;\r\n        const day = parts.find(part => part.type === 'day').value;\r\n        const hour = parts.find(part => part.type === 'hour').value;\r\n        const minute = parts.find(part => part.type === 'minute').value;\r\n        const second = parts.find(part => part.type === 'second').value;\r\n\r\n        return `${year}-${month}-${day} ${hour}:${minute}:${second}`;\r\n      } catch (error) {\r\n        // 如果时区处理失败，回退到UTC时间\r\n        console.warn(`时区格式化失败，使用UTC时间: ${error.message}`);\r\n        return date.toISOString().replace('T', ' ').substring(0, 19);\r\n      }\r\n    }\r\n\r\n    async function createForumTopic(userName, nickname, userId) {\r\n      const response = await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/createForumTopic`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ chat_id: GROUP_ID, name: `${nickname}` })\r\n      });\r\n      const data = await response.json();\r\n      if (!data.ok) throw new Error(`Failed to create forum topic: ${data.description}`);\r\n      const topicId = data.result.message_thread_id;\r\n\r\n      const now = new Date();\r\n      const formattedTime = formatTimeWithTimezone(now, 'Asia/Shanghai');\r\n      const notificationContent = await getNotificationContent();\r\n      const pinnedMessage = `昵称: ${nickname}\\n用户名: @${userName || 'N/A'}\\nUserID: ${userId}\\n发起时间: ${formattedTime}\\n\\n${notificationContent}`;\r\n      const messageResponse = await sendMessageToTopic(topicId, pinnedMessage);\r\n      const messageId = messageResponse.result.message_id;\r\n      await pinMessage(topicId, messageId);\r\n\r\n      return topicId;\r\n    }\r\n\r\n    async function saveTopicId(chatId, topicId) {\r\n      await env.D1.prepare('INSERT OR REPLACE INTO chat_topic_mappings (chat_id, topic_id) VALUES (?, ?)')\r\n          .bind(chatId, topicId)\r\n          .run();\r\n      topicIdCache.set(chatId, topicId);\r\n    }\r\n\r\n    async function getPrivateChatId(topicId) {\r\n      for (const [chatId, tid] of topicIdCache.cache) if (tid === topicId) return chatId;\r\n      const mapping = await env.D1.prepare('SELECT chat_id FROM chat_topic_mappings WHERE topic_id = ?')\r\n          .bind(topicId)\r\n          .first();\r\n      return mapping?.chat_id || null;\r\n    }\r\n\r\n    async function sendMessageToTopic(topicId, text) {\r\n      if (!text.trim()) {\r\n        throw new Error('Message text is empty');\r\n      }\r\n\r\n      const requestBody = {\r\n        chat_id: GROUP_ID,\r\n        text: text,\r\n        message_thread_id: topicId\r\n      };\r\n      const response = await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/sendMessage`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(requestBody)\r\n      });\r\n      const data = await response.json();\r\n      if (!data.ok) {\r\n        throw new Error(`Failed to send message to topic ${topicId}: ${data.description}`);\r\n      }\r\n      return data;\r\n    }\r\n\r\n    async function copyMessageToTopic(topicId, message) {\r\n      const requestBody = {\r\n        chat_id: GROUP_ID,\r\n        from_chat_id: message.chat.id,\r\n        message_id: message.message_id,\r\n        message_thread_id: topicId,\r\n        disable_notification: true\r\n      };\r\n      const response = await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/copyMessage`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(requestBody)\r\n      });\r\n      const data = await response.json();\r\n      if (!data.ok) {\r\n        throw new Error(`Failed to copy message to topic ${topicId}: ${data.description}`);\r\n      }\r\n    }\r\n\r\n    async function pinMessage(topicId, messageId) {\r\n      const requestBody = {\r\n        chat_id: GROUP_ID,\r\n        message_id: messageId,\r\n        message_thread_id: topicId\r\n      };\r\n      const response = await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/pinChatMessage`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(requestBody)\r\n      });\r\n      const data = await response.json();\r\n      if (!data.ok) {\r\n        throw new Error(`Failed to pin message: ${data.description}`);\r\n      }\r\n    }\r\n\r\n    async function forwardMessageToPrivateChat(privateChatId, message) {\r\n      const requestBody = {\r\n        chat_id: privateChatId,\r\n        from_chat_id: message.chat.id,\r\n        message_id: message.message_id,\r\n        disable_notification: true\r\n      };\r\n      const response = await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/copyMessage`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(requestBody)\r\n      });\r\n      const data = await response.json();\r\n      if (!data.ok) {\r\n        throw new Error(`Failed to forward message to private chat: ${data.description}`);\r\n      }\r\n    }\r\n\r\n    async function sendMessageToUser(chatId, text, options = {}) {\r\n      const payload = {\r\n        chat_id: chatId,\r\n        text: text,\r\n        parse_mode: 'HTML',\r\n        disable_web_page_preview: true,\r\n        ...options\r\n      };\r\n\r\n      const response = await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/sendMessage`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(payload)\r\n      });\r\n\r\n      const data = await response.json();\r\n      if (!data.ok) {\r\n        throw new Error(`发送消息失败: ${data.description}`);\r\n      }\r\n      return data.result;\r\n    }\r\n\r\n    async function fetchWithRetry(url, options, retries = RETRY_CONFIG.maxRetries) {\r\n      let lastError;\r\n\r\n      for (let i = 0; i < retries; i++) {\r\n        try {\r\n          const controller = new AbortController();\r\n          const timeoutId = setTimeout(() => controller.abort(), 10000); // 增加超时时间\r\n\r\n          const response = await fetch(url, {\r\n            ...options,\r\n            signal: controller.signal\r\n          });\r\n          clearTimeout(timeoutId);\r\n\r\n          if (response.ok) {\r\n            return response;\r\n          }\r\n\r\n          if (response.status === 429) {\r\n            const retryAfter = response.headers.get('Retry-After') || 5;\r\n            const delay = Math.min(parseInt(retryAfter) * 1000, RETRY_CONFIG.maxDelay);\r\n            await new Promise(resolve => setTimeout(resolve, delay));\r\n            continue;\r\n          }\r\n\r\n          if (response.status >= 500) {\r\n            throw new Error(`服务器错误: ${response.status}`);\r\n          }\r\n\r\n          return response;\r\n        } catch (error) {\r\n          lastError = error;\r\n          if (i < retries - 1) {\r\n            const delay = Math.min(RETRY_CONFIG.baseDelay * Math.pow(2, i), RETRY_CONFIG.maxDelay);\r\n            await new Promise(resolve => setTimeout(resolve, delay));\r\n          }\r\n        }\r\n      }\r\n\r\n      console.error(`Fetch failed after ${retries} retries for ${url}:`, lastError);\r\n      throw lastError;\r\n    }\r\n\r\n    async function registerWebhook(request) {\r\n      const webhookUrl = `${new URL(request.url).origin}/webhook`;\r\n      const response = await fetch(`https://api.telegram.org/bot${BOT_TOKEN}/setWebhook`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ url: webhookUrl })\r\n      }).then(r => r.json());\r\n      return new Response(response.ok ? 'Webhook set successfully' : JSON.stringify(response, null, 2));\r\n    }\r\n\r\n    async function unRegisterWebhook() {\r\n      const response = await fetch(`https://api.telegram.org/bot${BOT_TOKEN}/setWebhook`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ url: '' })\r\n      }).then(r => r.json());\r\n      return new Response(response.ok ? 'Webhook removed' : JSON.stringify(response, null, 2));\r\n    }\r\n\r\n    try {\r\n      return await handleRequest(request);\r\n    } catch (error) {\r\n      return new Response('Internal Server Error', { status: 500 });\r\n    }\r\n  }\r\n};
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/_worker.js b/_worker.js
--- a/_worker.js	(revision a43e7e94f1ca9c93886b1484b03cd7b39e43a50c)
+++ b/_worker.js	(date 1754300731628)
@@ -10,6 +10,15 @@
 
 const topicCreationLocks = new Map();
 
+// 常量和工具函数
+const getCurrentTimestamp = () => Math.floor(Date.now() / 1000);
+const cleanupProcessedItems = (itemSet) => {
+  if (itemSet.size > 1000) {
+    const items = Array.from(itemSet).slice(0, 500);
+    items.forEach(item => itemSet.delete(item));
+  }
+};
+
 const settingsCache = new Map([
   ['verification_enabled', null],
   ['user_raw_enabled', null]
@@ -45,7 +54,7 @@
   }
 }
 
-const userInfoCache = new LRUCache(500);  // 减少缓存大小
+const userInfoCache = new LRUCache(500);
 const topicIdCache = new LRUCache(500);
 const userStateCache = new LRUCache(500);
 const messageRateCache = new LRUCache(500);
@@ -263,7 +272,7 @@
         return;
       }
 
-      const nowSeconds = Math.floor(now / 1000);
+      const nowSeconds = getCurrentTimestamp();
       const expiredCodes = await d1.prepare(
           'SELECT chat_id FROM user_states WHERE code_expiry IS NOT NULL AND code_expiry < ?'
       ).bind(nowSeconds).all();
@@ -288,10 +297,7 @@
           processedMessages.add(messageKey);
 
           // 清理旧消息记录
-          if (processedMessages.size > 1000) {
-            const oldMessages = Array.from(processedMessages).slice(0, 500);
-            oldMessages.forEach(key => processedMessages.delete(key));
-          }
+          cleanupProcessedItems(processedMessages);
 
           await onMessage(update.message);
         } else if (update.callback_query) {
@@ -300,10 +306,7 @@
           processedCallbacks.add(callbackKey);
 
           // 清理旧回调记录
-          if (processedCallbacks.size > 1000) {
-            const oldCallbacks = Array.from(processedCallbacks).slice(0, 500);
-            oldCallbacks.forEach(key => processedCallbacks.delete(key));
-          }
+          cleanupProcessedItems(processedCallbacks);
 
           await onCallbackQuery(update.callback_query);
         }
@@ -361,7 +364,7 @@
       if (!verificationEnabled) {
         // 验证码关闭时，所有用户都可以直接发送消息
       } else {
-        const nowSeconds = Math.floor(Date.now() / 1000);
+        const nowSeconds = getCurrentTimestamp();
         const isVerified = userState.is_verified && userState.verified_expiry && nowSeconds < userState.verified_expiry;
         const isFirstVerification = userState.is_first_verification;
         const isRateLimited = await checkMessageRate(chatId);
@@ -374,7 +377,6 @@
                 .bind(chatId)
                 .first();
 
-            const nowSeconds = Math.floor(Date.now() / 1000);
             const isCodeExpired = !storedCode?.verification_code || !storedCode?.code_expiry || nowSeconds > storedCode.code_expiry;
 
             if (isCodeExpired) {
@@ -612,82 +614,59 @@
       return message;
     }
 
-    async function getNotificationContent() {
-      return MESSAGES.NOTIFICATION;
-    }
+    const getNotificationContent = () => MESSAGES.NOTIFICATION;
 
-    async function checkStartCommandRate(chatId) {
+    // 通用速率限制检查函数
+    async function checkRateLimit(chatId, type = 'message') {
       const now = Date.now();
-      const window = 5 * 60 * 1000;
-      const maxStartsPerWindow = 1;
+      const isStartCommand = type === 'start';
+
+      const config = isStartCommand ? {
+        window: 5 * 60 * 1000, // 5分钟
+        maxCount: 1,
+        countField: 'start_count',
+        windowField: 'start_window_start'
+      } : {
+        window: 60 * 1000, // 1分钟
+        maxCount: MAX_MESSAGES_PER_MINUTE,
+        countField: 'message_count',
+        windowField: 'window_start'
+      };
 
       await env.D1.prepare('INSERT OR IGNORE INTO message_rates (chat_id) VALUES (?)').bind(chatId).run();
 
       const result = await env.D1.prepare(
          `UPDATE message_rates
           SET
-            start_count = CASE
-              WHEN ? - COALESCE(start_window_start, 0) > ? THEN 1
-              ELSE start_count + 1
+            ${config.countField} = CASE
+              WHEN ? - COALESCE(${config.windowField}, 0) > ? THEN 1
+              ELSE ${config.countField} + 1
             END,
-            start_window_start = CASE
-              WHEN ? - COALESCE(start_window_start, 0) > ? THEN ?
-              ELSE COALESCE(start_window_start, ?)
+            ${config.windowField} = CASE
+              WHEN ? - COALESCE(${config.windowField}, 0) > ? THEN ?
+              ELSE COALESCE(${config.windowField}, ?)
             END
           WHERE chat_id = ?
-          RETURNING start_count, start_window_start`
-      ).bind(now, window, now, window, now, now, chatId).first();
+          RETURNING ${config.countField}, ${config.windowField}`
+      ).bind(now, config.window, now, config.window, now, now, chatId).first();
 
       if (!result) {
-        console.error(`Failed to update start command rate for chat_id: ${chatId}`);
+        console.error(`Failed to update ${type} rate for chat_id: ${chatId}`);
         return true; // Fail safe, assume rate limited.
       }
 
       const cachedData = messageRateCache.get(chatId) || {};
       messageRateCache.set(chatId, {
         ...cachedData,
-        start_count: result.start_count,
-        start_window_start: result.start_window_start,
+        [config.countField]: result[config.countField],
+        [config.windowField]: result[config.windowField],
       });
 
-      return result.start_count > maxStartsPerWindow;
+      return result[config.countField] > config.maxCount;
     }
-
-    async function checkMessageRate(chatId) {
-      const now = Date.now();
-      const window = 60 * 1000;
-
-      await env.D1.prepare('INSERT OR IGNORE INTO message_rates (chat_id) VALUES (?)').bind(chatId).run();
-
-      const result = await env.D1.prepare(
-         `UPDATE message_rates
-          SET
-            message_count = CASE
-              WHEN ? - COALESCE(window_start, 0) > ? THEN 1
-              ELSE message_count + 1
-            END,
-            window_start = CASE
-              WHEN ? - COALESCE(window_start, 0) > ? THEN ?
-              ELSE COALESCE(window_start, ?)
-            END
-          WHERE chat_id = ?
-          RETURNING message_count, window_start`
-      ).bind(now, window, now, window, now, now, chatId).first();
-
-      if (!result) {
-        console.error(`Failed to update message rate for chat_id: ${chatId}`);
-        return true; // Fail safe, assume rate limited.
-      }
 
-      const cachedData = messageRateCache.get(chatId) || {};
-      messageRateCache.set(chatId, {
-        ...cachedData,
-        message_count: result.message_count,
-        window_start: result.window_start,
-      });
-
-      return result.message_count > MAX_MESSAGES_PER_MINUTE;
-    }
+    const checkStartCommandRate = (chatId) => checkRateLimit(chatId, 'start');
+    const checkMessageRate = (chatId) => checkRateLimit(chatId, 'message');
 
     async function getSetting(key, d1) {
       if (settingsCache.has(key)) {
@@ -715,8 +694,8 @@
       if (key === 'verification_enabled') {
         settingsCache.set('verification_enabled', value === 'true');
         if (value === 'false') {
-          const nowSeconds = Math.floor(Date.now() / 1000);
-          const verifiedExpiry = nowSeconds + 3600 * 24;
+          const nowSeconds = getCurrentTimestamp();
+          const verifiedExpiry = nowSeconds + 3600 * 24; // 24小时
           await env.D1.prepare('UPDATE user_states SET is_verified = ?, verified_expiry = ?, is_verifying = ?, verification_code = NULL, code_expiry = NULL, is_first_verification = ? WHERE is_blocked = FALSE')
               .bind(true, verifiedExpiry, false, false)
               .run();
@@ -788,7 +767,7 @@
 
         const storedCode = verificationState.verification_code;
         const codeExpiry = verificationState.code_expiry;
-        const nowSeconds = Math.floor(Date.now() / 1000);
+        const nowSeconds = getCurrentTimestamp();
 
         if (!storedCode || (codeExpiry && nowSeconds > codeExpiry)) {
           await handleExpiredVerification(chatId, messageId);
@@ -796,7 +775,7 @@
         }
 
         if (result === 'correct') {
-          const verifiedExpiry = nowSeconds + 3600 * 24;
+          const verifiedExpiry = nowSeconds + 3600 * 24; // 24小时
           await env.D1.prepare('UPDATE user_states SET is_verified = ?, verified_expiry = ?, verification_code = NULL, code_expiry = NULL, last_verification_message_id = NULL, is_first_verification = ?, is_verifying = ? WHERE chat_id = ?')
               .bind(true, verifiedExpiry, false, false, chatId)
               .run();
@@ -993,8 +972,8 @@
         }))];
 
         const question = `🔢 请完成验证：${num1} ${operation} ${num2} = ?\n⏰ 验证码5分钟内有效`;
-        const nowSeconds = Math.floor(Date.now() / 1000);
-        const codeExpiry = nowSeconds + 300;
+        const nowSeconds = getCurrentTimestamp();
+        const codeExpiry = nowSeconds + 300; // 5分钟
 
         const response = await fetchWithRetry(`https://api.telegram.org/bot${BOT_TOKEN}/sendMessage`, {
           method: 'POST',
@@ -1136,7 +1115,7 @@
 
       const now = new Date();
       const formattedTime = formatTimeWithTimezone(now, 'Asia/Shanghai');
-      const notificationContent = await getNotificationContent();
+      const notificationContent = getNotificationContent();
       const pinnedMessage = `昵称: ${nickname}\n用户名: @${userName || 'N/A'}\nUserID: ${userId}\n发起时间: ${formattedTime}\n\n${notificationContent}`;
       const messageResponse = await sendMessageToTopic(topicId, pinnedMessage);
       const messageId = messageResponse.result.message_id;
@@ -1264,7 +1243,7 @@
       for (let i = 0; i < retries; i++) {
         try {
           const controller = new AbortController();
-          const timeoutId = setTimeout(() => controller.abort(), 10000); // 增加超时时间
+          const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒
 
           const response = await fetch(url, {
             ...options,
